/* You can add global styles to this file, and also import other style files */

@import "./assets/scss/font";
@import "./assets/scss/buttons/base";
@import "./assets/scss/variables";
@import "@ng-select/ng-select/themes/default.theme.css";

body,
input,
textarea,
select {
  font-family: "DINNextLTArabic" !important;
}
html[dir="rtl"] {
  direction: rtl !important;
  text-align: right;
  
}

html[dir="ltr"] {
  direction: ltr !important;
  text-align: left;
  .breadcrumb-container {
    left: 18rem;
    right: auto;
    padding-left: 2rem;
  }
  .form-container .row {
    direction: ltr !important;
  }
  .input-group-text {
    right: 5px;
    left: auto;
  }
  .rotate-icon{
    transform: rotate(180deg);
  }
  .sidenav{
    .list-unstyled {
      padding-left: 2rem !important;
    }
  }

}

:root[dir="rtl"] {
  body {
    text-align: right;
    direction: rtl;
  }
}

:root[dir="ltr"] {
  body {
    text-align: left;
    direction: ltr;
  }
}
::ng-deep .ng-dropdown-panel {
  z-index: 9999 !important;
}
::ng-deep .cdk-overlay-container {
  z-index: 10000 !important;
}

textarea::placeholder {
  color: #bdbdbd !important;
  // background-color: #BDBDBD;
}

:root.light-theme {
  --bg-color: #ffffff;
  --text-color: #000000;
}

:root.dark-theme {
  --bg-color: #1a1a1a;
  --text-color: #ffffff;
}

:root.blue-theme {
  --bg-color: #010853;
  --text-color: #ffffff;
}

body {
  background-color: var(--bg-color);
  color: var(--text-color);
}

.header {
  color: $navy-blue;
}
.text-primary {
  color: $navy-blue !important;
}
.fs-20 {
  font-size: 20px;
}
.flex-wrap-wrap {
  flex-wrap: wrap;
}
@media (min-width: 992px) {
  .position-lg-fixed {
    position: fixed !important;
    //  z-index: 1030; // above most content
  }
}

.modal-dialog {
  .mat-mdc-dialog-container {
    padding: 0 !important;
    border-radius: 8px;
    box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.14);
  }

  .mdc-dialog__surface {
    background-color: #ffffff !important;
  }
}

.custom-file-upload {
  position: relative;
  input[type="file"] {
    display: block;
    width: 100%;
    padding: 6px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }

  .file-preview {
    margin-top: 8px;
    font-size: 0.9rem;
    color: #555;
  }
}

.ng-select.ng-select-single .ng-select-container {
  height: 42px;
}
.ng-dropdown-panel.ng-select-bottom {
  top: 90%;
}
.mat-mdc-radio-button
  .mdc-radio__native-control:enabled:checked
  + .mdc-radio__background
  .mdc-radio__outer-circle,
.mat-mdc-radio-button
  .mdc-radio__native-control:enabled:checked
  + .mdc-radio__background
  .mdc-radio__inner-circle {
  border-color: $navy-blue !important;
}
.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element,
.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before {
  background-color: $navy-blue !important;
}
.custom-ng-select.ng-select.multiple .ng-value {
  background-color: #e6f0ff; // light blue
  color: #003366; // dark text
  border-radius: 20px;
  padding: 4px 12px;
  margin: 2px 4px;
  font-size: 14px;
}

.custom-ng-select.ng-select .ng-value-icon {
  color: #003366;
  margin-left: 4px;
}

.custom-ng-select .ng-select-container {
  border: 1px solid $mid-gray-bg;
  border-radius: 6px;
}

.ng-select.ng-select-disabled > .ng-select-container {
  background-color: $inactive-bg;
  color: $text-grey;
  cursor: not-allowed;
}
.ng-select.ng-select-multiple
  .ng-select-container
  .ng-value-container
  .ng-value {
  display: none !important;
}
.ng-select.ng-select-multiple
  .ng-select-container
  .ng-value-container
  .ng-placeholder {
  position: relative !important;
  top: 0;
}
.ng-placeholder {
  padding: 0 3px !important;
}

.tag {
  display: flex;
  min-width: 24px;
  padding: 10px;
  justify-content: center;
  align-items: center;
  gap: 2px;
  background: var(--Brand-Background-2-Rest, rgba(235, 243, 252, 1));
  border: 1px solid var(--Brand-Stroke-2-Rest, rgba(180, 214, 250, 1));
  border-radius: 2rem;
}
.text-grey {
  color: $text-grey;
}
.fs-14 {
  font-size: 14px;
}
.is-invalid {
  color: $system-red;
  &.custom-ng-select .ng-select-container {
    border-color: $system-red;
  }
  .form-control {
    border-color: $system-red;
  }
}
.error-message {
  font-size: 12px;
  margin-top: 4px;
  color: #c50f1f;
}
.ng-select.ng-select-disabled .ng-arrow-wrapper {
  display: none;
}

.ng-select .ng-select-container {
  height: 42px;
}
.mat-mdc-radio-button .mdc-radio {
  padding: 0;
}

.mat-mdc-radio-button .mdc-radio__inner-circle {
  background-color: $navy-blue !important;
}

// styles.scss

$placeholder-color: #bdbdbd;

@mixin placeholder-style {
  font-weight: 400;
  font-size: 16px;
  line-height: 22.4px;
  letter-spacing: 0%;
  vertical-align: bottom;
  color: $placeholder-color !important;
}

input::placeholder,
textarea::placeholder {
  @include placeholder-style;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  @include placeholder-style;
}

// Firefox
input::-moz-placeholder,
textarea::-moz-placeholder {
  @include placeholder-style;
  opacity: 1;
}

// IE/Edge
input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  @include placeholder-style;
}

// ::ng-deep for Angular Material & ng-select
::ng-deep .mat-input-element::placeholder,
::ng-deep .ng-select .ng-placeholder {
  @include placeholder-style;
}
::placeholder,
input::placeholder,
textarea::placeholder,
.mat-input-element::placeholder,
.ng-select .ng-placeholder {
  font-weight: 400;
  font-size: 16px;
  line-height: 22.4px;
  letter-spacing: 0;
  vertical-align: bottom;
  color: $placeholder-color;
}


.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;

  .dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin:0 6px;
  }

  &.status-green {
    background-color: #f1faf1;
    color: #27ae60;

    .dot {
      background-color: #27ae60;
    }
  }

  &.status-blue {
    background-color: #e5eefb;
    color: #2f80ed;

    .dot {
      background-color: #2f80ed;
    }
  }
  
  &.status-orange {
    background-color: #fdf1eb;
    color: #ff5f3d;
    
    .dot {
      background-color: #ff5f3d;
    }
  }
  
  &.status-grey {
    background-color: #e0e0e0;
    color: #828282;
    
    .dot {
      background-color: #828282;
    }
  }

  &.status-info {
    background-color: rgba(47, 128, 237, 0.1);
    color: #2f80ed;
  
    .dot {
      background-color: #2f80ed;
    }
  }
}
.mat-mdc-dialog-surface{
  overflow: visible !important;
}