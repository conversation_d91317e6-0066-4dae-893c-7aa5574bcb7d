import { SingleHolderRoleAvailabilityResponse } from './../../../../core/api/api.generated';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil, catchError, of, finalize } from 'rxjs';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import Swal from 'sweetalert2';

// Shared imports
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { ErrorModalService } from '@core/services/error-modal.service';

// Core imports
import { AuthorzationServiceProxy } from '@core/api/api.generated';
import { UserManagementService } from '@shared/services/users/user-management.service';

// Enums and interfaces
import { InputType } from '@shared/enum/input-type.enum';
import { ButtonTypeEnum } from '@core/enums/icon-enum';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';

// Validators
import {
  saudiIbanValidator,
  saudiPassportValidator,
} from '@shared/validators/saudi-validators';
import { saudiPhoneValidator } from '@shared/validators/saudi-phone.validator';

@Component({
  selector: 'app-edit-user',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    FormBuilderComponent,
    BreadcrumbComponent,
    PageHeaderComponent,
    CustomButtonComponent,
  ],
  templateUrl: './edit-user.component.html',
  styleUrls: ['./edit-user.component.scss'],
})
export class EditUserComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  editUserForm!: FormGroup;
  formControls: IControlOption[] = [];
  breadcrumbItems: IBreadcrumbItem[] = [];

  isLoading = false;
  isFormSubmitted = false;
  userId!: number;
  currentUserData: any = null;
  uniqueRoles: string[] = [];
  singleRoleAvailability: SingleHolderRoleAvailabilityResponse | null = null;

  // Enums for template
  ButtonTypeEnum = ButtonTypeEnum;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private userManagementService: UserManagementService,
    private authorizationService: AuthorzationServiceProxy,
    private errorModalService: ErrorModalService,
    private translateService: TranslateService
  ) {
    this.initializeBreadcrumbs();
    this.loadRoles();
  }

  ngOnInit(): void {
    this.getUserIdFromRoute();
    this.initializeForm();
    this.setupFormControls();
    this.loadUserData();
    this.uniqueRoles  = [
      'legalcouncil',
      'financecontroller',
      'compliancelegalmanagingdirector',
      'headofrealestate',
    ];
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private getUserIdFromRoute(): void {
    this.userId = Number(this.route.snapshot.paramMap.get('id'));
    if (!this.userId) {
      this.errorModalService.showError('USER_MANAGEMENT.EDIT.INVALID_USER_ID');
      this.router.navigate(['/admin/user-management']);
    }
  }

  private initializeBreadcrumbs(): void {
    this.breadcrumbItems = [
      {
        label: 'COMMON.HOME',
        url: '/admin/dashboard',
      },
      {
        label: 'USER_MANAGEMENT.TITLE',
        url: '/admin/user-management',
      },
      {
        label: 'USER_MANAGEMENT.EDIT.PAGE_TITLE',
        disabled: true,
      },
    ];
  }

  private initializeForm(): void {
    this.editUserForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      mobile: ['', [Validators.required, saudiPhoneValidator()]], // 9 digits starting with 5 (Saudi phone format)
      iban: ['', [saudiIbanValidator()]], // Optional field with Saudi IBAN validation
      nationality: ['',[Validators.maxLength(100)]], // Optional field, no default value
      cv: [''],
      personalPhoto: [''],
      passportNo: ['', [Validators.required, saudiPassportValidator()]],
      roles: [[], [Validators.required]], // Changed to array to support multiple roles
      registrationMessageSent: [true],
      registrationCompleted: [false],
    });
  }

  private setupFormControls(): void {
    this.formControls = [
      // Basic Information Section
      {
        formControlName: 'name',
        type: InputType.Text,
        id: 'name',
        name: 'name',
        label: 'USER_MANAGEMENT.EDIT.NAME',
        placeholder: 'USER_MANAGEMENT.EDIT.NAME_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-4',
        maxLength: 100,
      },
      {
        formControlName: 'email',
        type: InputType.Email,
        id: 'email',
        name: 'email',
        label: 'USER_MANAGEMENT.EDIT.EMAIL',
        placeholder: 'USER_MANAGEMENT.EDIT.EMAIL_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-4',
        maxLength: 100,
      },
      {
        formControlName: 'mobile',
        type: InputType.Tel,
        id: 'mobile',
        name: 'mobile',
        label: 'USER_MANAGEMENT.EDIT.PHONE_NUMBER',
        placeholder: 'USER_MANAGEMENT.CREATE.MOBILE_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-4',
      },

      // Personal Details
      {
        formControlName: 'nationality',
        type: InputType.Text,
        id: 'nationality',
        name: 'nationality',
        label: 'USER_MANAGEMENT.EDIT.NATIONALITY',
        placeholder: 'USER_MANAGEMENT.EDIT.NATIONALITY_PLACEHOLDER',
        isRequired: false, // Optional field according to User Story 1223
        class: 'col-md-4',
        maxLength: 100, // Max 100 characters as per User Story 1223
      },
      {
        formControlName: 'passportNo',
        type: InputType.Mixed,
        id: 'passportNo',
        name: 'passportNo',
        label: 'USER_MANAGEMENT.EDIT.PASSPORT_NO',
        placeholder: 'USER_MANAGEMENT.EDIT.PASSPORT_NO_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-4',
        maxLength: 9, // 1 letter + 8 digits
        pattern: '^[A-Z][0-9]{8}$', // Pattern hint for UI
      },

      {
        formControlName: 'iban',
        type: InputType.Mixed,
        id: 'iban',
        name: 'iban',
        label: 'USER_MANAGEMENT.EDIT.IBAN',
        placeholder: 'USER_MANAGEMENT.EDIT.IBAN_PLACEHOLDER',
        isRequired: false, // Optional field according to User Story 1223
        class: 'col-md-4',
        maxLength: 24, // SA + 22 digits
        pattern: '^SA[0-9]{22}$', // Pattern hint for UI
      },
      // System Access & Role Information
      {
        formControlName: 'roles',
        type: InputType.Dropdown,
        id: 'roles',
        name: 'roles',
        label: 'USER_MANAGEMENT.EDIT.ROLE',
        placeholder: 'USER_MANAGEMENT.EDIT.ROLE_PLACEHOLDER',
        isRequired: true,
        multiple: true,
        class: 'col-md-4',
        options: [],
      },

      {
        formControlName: '',
        type: InputType.empty,
        id: '',
        name: '',
        label: '',
        placeholder: '',
        class: 'col-md-4',
      },
      {
        formControlName: 'cv',
        type: InputType.file,
        id: 'cv',
        name: 'cv',
        label: 'USER_MANAGEMENT.EDIT.CV',
        placeholder: 'USER_MANAGEMENT.EDIT.CV_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-12',
        allowedTypes: ['pdf', 'doc', 'docx'],
        max: 10, // 10MB max for CV
      },
      // {
      //   formControlName: 'personalPhoto',
      //   type: InputType.file,
      //   id: 'personalPhoto',
      //   name: 'personalPhoto',
      //   label: 'USER_MANAGEMENT.EDIT.PERSONAL_PHOTO',
      //   placeholder: 'USER_MANAGEMENT.EDIT.PERSONAL_PHOTO_PLACEHOLDER',
      //   isRequired: false,
      //   class: 'col-md-6',
      //   allowedTypes: ['jpg', 'jpeg', 'png'],
      //   max: 2, // 2MB max for photo
      // },
    ];
  }

  private loadRoles(): void {
    this.userManagementService.getRolesList().subscribe((response: any) => {
      console.log('Roles API called successfully', response);
      if (response)
        this.formControls.find(
          (control) => control.formControlName === 'roles'
        )!.options = response.data.map((role: any) => ({
          id: role.roleName,
          name: role.displayName,
          value: role.displayName,
        }));
    });
  }

  private loadUserData(): void {
    this.isLoading = true;

    this.userManagementService
      .getUserById(this.userId)
      .subscribe((response) => {
        if (response) {
          this.populateForm(response);
        }
      });
  }

  /**
   * Extracts the 9-digit mobile number from the full mobile number
   * Handles cases where the number might include country code (+966 or 0)
   */
  private extractMobileNumber(fullMobileNumber: string): string {
    if (!fullMobileNumber) {
      return '';
    }

    // Remove any non-digit characters
    const digitsOnly = fullMobileNumber.replace(/\D/g, '');

    // Handle different formats:
    // 966501234567 -> 501234567
    // 0501234567 -> 501234567
    // 501234567 -> 501234567
    if (digitsOnly.startsWith('966') && digitsOnly.length === 12) {
      return digitsOnly.substring(3); // Remove 966 prefix
    } else if (digitsOnly.startsWith('0') && digitsOnly.length === 10) {
      return digitsOnly.substring(1); // Remove 0 prefix
    } else if (digitsOnly.length === 9 && digitsOnly.startsWith('5')) {
      return digitsOnly; // Already in correct format
    }

    // If none of the above, return the original (might be invalid)
    return digitsOnly;
  }

  private populateForm(userData: any): void {
    this.currentUserData = userData;
console.log('User data:', userData);
    try {
      const user = userData.data || userData;
      this.singleRoleAvailability = new SingleHolderRoleAvailabilityResponse();
      this.singleRoleAvailability.legalCouncilHasActiveUser = user.legalCouncilHasActiveUser;
      this.singleRoleAvailability.financeControllerHasActiveUser = user.financeControllerHasActiveUser;
      this.singleRoleAvailability.complianceLegalManagingDirectorHasActiveUser = user.complianceLegalManagingDirectorHasActiveUser;
      this.singleRoleAvailability.headOfRealEstateHasActiveUser = user.headOfRealEstateHasActiveUser;
      console.log('Role availability:', this.singleRoleAvailability);
      // Extract mobile number from userName (remove country code if present)
      const mobileNumber = this.extractMobileNumber(user.userName || '');

      this.editUserForm.patchValue({
        name: user.fullName || '',
        email: user.email || '',
        mobile: mobileNumber, // 9-digit mobile number for the Tel input
        nationality: user.nationality || '',
        passportNo: user.passportNo || '',
        roles: user.roles || [],
        iban: user.iban || '',
        cv: user.cvFilePath || '',
        personalPhoto: user.personalPhotoPath || '',
        registrationMessageSent: user.registrationMessageIsSent || false,
        registrationCompleted: user.registrationIsCompleted || false,
      });

      const control = this.formControls.find((c) => c.formControlName === 'cv');
      if (control) {
        const attachment = {
          id: 0,
          fileName: user.fullName + '_Cv',
          filePath: user.cvFilePath,
        };
        control.initialFiles = [attachment];
      }
      const controlPersonalPhoto = this.formControls.find(
        (c) => c.formControlName === 'personalPhoto'
      );
      if (controlPersonalPhoto) {
        const attachment = {
          id: 0,
          fileName: user.fullName + '_Photo',
          filePath: user.personalPhotoPath,
        };
        controlPersonalPhoto.initialFiles = [attachment];
      }
    } catch (error) {
      console.error('Error populating form:', error);
      this.errorModalService.showError('USER_MANAGEMENT.EDIT.POPULATE_ERROR');
    }
  }


  onValueChange(event: any, control: IControlOption): void {
    console.log('Value changed:', control.formControlName, event);
  }

  onKeyPressed(event: any, control: IControlOption): void {
    // Handle specific key press events if needed
  }

  onFileUploaded(data: any) {
    this.editUserForm
      .get(data.control.formControlName)
      ?.setValue(data.file.url);
  }
  onRemoveSelectedItem(data: any): void {
    if (data.control.formControlName == 'roles') {
      const current =
        this.editUserForm.get(data.control.formControlName)?.value || [];
      const updated = current.filter((id: any) => id !== data.idToRemove);
      this.editUserForm.get(data.control.formControlName)?.setValue(updated);
    }
  }
  onSubmit(): void {
    this.isFormSubmitted = true;
    if (this.editUserForm.valid && !this.isLoading) {
      this.isLoading = true;

      // Check for role availability conflicts before proceeding
      this.checkRoleAvailabilityConflicts(true)
        .then((shouldProceed: boolean) => {
          if (shouldProceed) {
            const editUserCommand = this.buildEditUserCommand();

            this.userManagementService
              .updateUser(editUserCommand)
              .pipe(
                takeUntil(this.destroy$),
                catchError((error) => {
                  console.error('Error updating user:', error);
                  this.errorModalService.showError(
                    'USER_MANAGEMENT.EDIT.UPDATE_ERROR'
                  );
                  return of(null);
                }),
                finalize(() => {
                  this.isLoading = false;
                  this.isFormSubmitted = false;
                })
              )
              .subscribe((response) => {
                if (response) {
                  this.isLoading = false;
                  this.errorModalService.showSuccess(
                    response.data ?? 'USER_MANAGEMENT.EDIT.SUCCESS'
                  );
                  this.router.navigate(['/admin/user-management']);
                }
              });
          } else {
            this.isLoading = false;
          }
        })
        .catch((error: any) => {
          console.error('Error checking role availability:', error);
          this.isLoading = false;
        });
    } else {
      this.isFormSubmitted = true;
      this.isLoading = false;
      // this.errorModalService.showError('USER_MANAGEMENT.EDIT.VALIDATION_ERROR');
    }
  }


  private buildEditUserCommand() {
    const formValue = this.editUserForm.value;

    // Format mobile number for API (ensure it starts with 05)
    const mobileNumber = formValue.mobile;
    const formattedMobile = mobileNumber && mobileNumber.startsWith('5') ? `0${mobileNumber}` : mobileNumber;

    const command = {
      id: this.userId,
      fullName: formValue.name,
      email: formValue.email,
      userName: formattedMobile, // Saudi mobile number as username (05XXXXXXX format)
      iban: formValue.iban,
      nationality: formValue.nationality,
      passportNo: formValue.passportNo,
      roles: formValue.roles,
      cvFile: formValue.cv,
      personalPhoto: formValue.personalPhoto,
      registrationMessageSent: formValue.registrationMessageSent,
      registrationCompleted: formValue.registrationCompleted,
    };

    return command;
  }

  onCancel(): void {
    this.router.navigate(['/admin/user-management']);
  }


    onDropdownChange(event: any, control: IControlOption): void {
      // Handle role selection logic
      if (control.formControlName === 'roles') {
        this.handleRoleChange(event);
      }
    }

    /**
     * Enhanced role selection logic based on business requirements
     * Handles multi-select enablement and role conflict detection
     */
    private handleRoleChange(selectedRoles: string[]): void {
      console.log('Role change detected:', selectedRoles);
      if (!selectedRoles || !Array.isArray(selectedRoles)) {
        return;
      }

      // Get the roles control from formControls
      const rolesControl = this.formControls.find(
        (control) => control.formControlName === 'roles'
      );
      if (!rolesControl) {
        return;
      }

      // Check if multi-select should be enabled based on current selection
      var shouldEnableMultiSelect =   this.shouldEnableMultiSelect(selectedRoles);

      // If multi-select is disabled and more than one role is selected, keep only the last selected
      if (!shouldEnableMultiSelect && selectedRoles.length > 1) {

        debugger;
        const lastSelected = selectedRoles[selectedRoles.length - 1] as  any;
        this.removeRoleFromSelection(lastSelected.name)
        // const current = this.createUserForm.get('roles')?.value || [];
        // const updated = current.filter((id: any) => id !== lastSelected.id  );
        // this.createUserForm.get('roles')?.setValue(updated);
        // selectedRoles.pop();
      }

      // Check for role conflicts with existing users
      this.checkRoleAvailabilityConflicts();
    }

    /**
     * Determines if multi-select should be enabled based on selected roles
     * Multi-select is enabled ONLY for these specific combinations:
     * 1. 'Fund Manager' AND 'Board Member' (both selected together)
     * 2. 'Associate Fund Manager' AND 'Board Member' (both selected together)
     */
    private shouldEnableMultiSelect(selectedRoles: string[]): boolean {
      console.log('shouldEnableMultiSelect called with roles:', selectedRoles);
      var valid = true;
      if (!selectedRoles || selectedRoles.length === 0) {
        valid = false;
      }
      // Normalize role names for comparison
      const normalizedRoles = selectedRoles.map((role) =>
        this.normalizeRoleName(role)
      ).filter(role => role); // Filter out undefined/null values

      // Multi-select is enabled only for exactly 2 roles in valid combinations
      // if (normalizedRoles.length !== 2) {
      //   return false;
      // }
      selectedRoles.forEach((roleName) => {
        const normalizedRole = this.normalizeRoleName(roleName);
        if (this.uniqueRoles.includes(normalizedRole)) {
           valid= false;
        }

      });
      // Valid combinations that enable multi-select:
      // 1. Fund Manager + Board Member
      // 2. Associate Fund Manager + Board Member
      const validCombinations = [
        ['fundmanager', 'boardmember'],
        ['associatedfundmanager', 'boardmember'],
      ];

      if(selectedRoles.length === 2){
         valid = validCombinations.some((combination) => {
         return combination.length === normalizedRoles.length && combination.every((role) => normalizedRoles.includes(role));
      });
      }

      console.log('Valid combination:', valid);
      // Check if current selection exactly matches any valid combination
      return valid;
    }

    /**
     * Normalizes role names to handle different formats (display names vs API names)
     * Handles case-insensitive matching and various role name formats
     */
    private normalizeRoleName(roleName: any): string {
      if (!roleName) {
        return '';
      }
      let roleNameStr: string;
      if (typeof roleName === 'string') {
        roleNameStr = roleName;
      } else if (typeof roleName === 'object') {
        // Handle role objects - try common property names
        roleNameStr = roleName.name;
      } else {
        roleNameStr = String(roleName);
      }
      // Convert to lowercase for case-insensitive comparison
      const lowerRoleName = roleNameStr.toLowerCase().trim();

      const roleMap: { [key: string]: string } = {
        // Fund Manager variations
        'fundmanager': 'fundmanager',
        // Associate Fund Manager variations
        'associatedfundmanager': 'associatedfundmanager',
        // Board Member variations
        'boardmember': 'boardmember',
        // Other roles
        'legalcouncil': 'legalcouncil',
        'boardsecretary': 'boardsecretary',
        'financecontroller': 'financecontroller',
        'compliancelegalmanagingdirector': 'compliancelegalmanagingdirector',
        'headofrealestate': 'headofrealestate',
      };

      return roleMap[lowerRoleName] || '';
    }

    /**
     * Removes a specific role from the current selection
     */
    private removeRoleFromSelection(roleToRemove: string): void {
      const currentRoles = this.editUserForm.get('roles')?.value || [];
      const updatedRoles = currentRoles.filter(
        (role: string) => role !== roleToRemove
      );
      this.editUserForm.get('roles')?.setValue(updatedRoles);

      // Re-evaluate multi-select settings
      this.handleRoleChange(updatedRoles);
    }

    /**
     * Checks for role availability conflicts before form submission
     * Returns a Promise that resolves to true if user should proceed, false otherwise
     */
    private async checkRoleAvailabilityConflicts(isformSubmission?: boolean): Promise<boolean> {
      const selectedRoles = this.editUserForm.get('roles')?.value || [];

      if (!selectedRoles.length || !this.singleRoleAvailability) {
        return true; // No roles selected or no availability data, proceed
      }

      // Map role names to availability flags
      const roleAvailabilityMap: { [key: string]: string } = {
        'legalcouncil': 'legalCouncilHasActiveUser',
        'financecontroller': 'financeControllerHasActiveUser',
        'compliancelegalmanagingdirector': 'complianceLegalManagingDirectorHasActiveUser',
        'headofrealestate': 'headOfRealEstateHasActiveUser',
      };

      // Find roles that are already taken
      const conflictingRoles: string[] = [];
console.log('Role availability:', this.singleRoleAvailability);
type AvailabilityFlag = keyof SingleHolderRoleAvailabilityResponse;
selectedRoles.forEach((roleName: string) => {
  const normalizedRole = this.normalizeRoleName(roleName);
  const availabilityFlag = roleAvailabilityMap[normalizedRole] || roleAvailabilityMap[roleName];
  if (
    availabilityFlag &&
    (this.singleRoleAvailability as Record<string, any>)[availabilityFlag] === true
  ) {
    conflictingRoles.push(roleName);
  }
});
debugger;
      // If no conflicts, proceed
      if (conflictingRoles.length === 0) {
        return true;
      }

      // Show confirmation dialog for conflicting roles
      return isformSubmission? this.showRoleAvailabilityConfirmation(conflictingRoles):false;
    }

    /**
     * Shows confirmation dialog for roles that are already taken
     */
    private showRoleAvailabilityConfirmation(conflictingRoles: string[]): Promise<boolean> {
      return new Promise((resolve) => {
        const rolesList = conflictingRoles.join(', ');

        Swal.fire({
          title: this.translateService.instant('USER_MANAGEMENT.ROLE_AVAILABILITY.TITLE'),
          text: this.translateService.instant('USER_MANAGEMENT.ROLE_AVAILABILITY.MESSAGE', {
            roles: rolesList
          }),
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#d33',
          cancelButtonColor: '#6c757d',
          confirmButtonText: this.translateService.instant('USER_MANAGEMENT.ROLE_AVAILABILITY.CONFIRM'),
          cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
        }).then((result) => {
          resolve(result.isConfirmed);
        });
      });
    }
}
