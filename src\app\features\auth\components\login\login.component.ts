import { Component, Inject, OnInit, inject } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CustomButtonComponent } from '../../../../shared/components/custom-button/custom-button.component';
import { IconEnum } from '@core/enums/icon-enum';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { LanguageService } from '@core/gl-services/language-services/language.service';
import {
  AuthenticationServiceProxy,
  IJwtAuthResponseBaseResponse,
  SignInCommand,
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { AuthService } from '../../services/auth-service/auth.service';
import { TokenService } from '../../services/token.service';
@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    CustomButtonComponent,
  ],
})
export class LoginComponent {
  loginForm: FormGroup;
  showPassword = false;
  currentLang:any;
  lang:any;



  iconName = IconEnum.arrowRight;

  constructor(
    private formBuilder: FormBuilder,
    private apiClient: AuthenticationServiceProxy,
    private router: Router,
    private languageService: LanguageService,
    private errorModalService: ErrorModalService,
    private authService: AuthService,
    private tokenService: TokenService,
    private translateService: TranslateService
  ) {
    this.loginForm = this.formBuilder.group({
      username: ['', [Validators.required]],
      password: ['', [Validators.required]],
    });
     this.lang = JSON.parse(localStorage.getItem('lang') || '""');
    this.currentLang = this.lang === 'en' ? LanguageEnum.en : LanguageEnum.ar;
  }


  changeLanguage(): void {
    this.currentLang =
      this.currentLang === LanguageEnum.en ? LanguageEnum.ar : LanguageEnum.en;
      this.languageService.switchLang(this.currentLang,true);
  }

  selectLanguage(lang: string, event: Event): void {
    event.preventDefault();
    const newLang = lang === 'en' ? LanguageEnum.en : LanguageEnum.ar;
    this.currentLang = newLang;
    this.languageService.switchLang(this.currentLang, true);
  }

  getCurrentLanguageFlag(): string {
    return this.currentLang === LanguageEnum.en ? this.getEnglishFlag() : this.getArabicFlag();
  }

  getCurrentLanguageText(): string {
    return this.currentLang === LanguageEnum.en ? 'English' : 'العربية';
  }

  getEnglishFlag(): string {
    return `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <g clip-path="url(#clip0_7132_166006)">
        <mask id="mask0_7132_166006" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
          <path d="M8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16Z" fill="white"/>
        </mask>
        <g mask="url(#mask0_7132_166006)">
          <path d="M8 0H16V2L15 3L16 4V6L15 7L16 8V10L15 11L16 12V14L8 15L0 14V12L1 11L0 10V8L8 0Z" fill="#EEEEEE"/>
          <path d="M7 2H16V4H7V2ZM7 6H16V8H8L7 6ZM0 10H16V12H0V10ZM0 14H16V16H0V14Z" fill="#D80027"/>
          <path d="M0 0H8V8H0V0Z" fill="#0052B4"/>
          <path d="M5.84375 7.59375L7.625 6.3125H5.4375L7.21875 7.59375L6.53125 5.5L5.84375 7.59375ZM3.3125 7.59375L5.09375 6.3125H2.90625L4.6875 7.59375L4 5.5L3.3125 7.59375ZM0.78125 7.59375L2.5625 6.3125H0.375L2.15625 7.59375L1.46875 5.5L0.78125 7.59375ZM5.84375 5.0625L7.625 3.78125H5.4375L7.21875 5.0625L6.53125 2.96875L5.84375 5.0625ZM3.3125 5.0625L5.09375 3.78125H2.90625L4.6875 5.0625L4 2.96875L3.3125 5.0625ZM0.78125 5.0625L2.5625 3.78125H0.375L2.15625 5.0625L1.46875 2.96875L0.78125 5.0625ZM5.84375 2.5L7.625 1.21875H5.4375L7.21875 2.5L6.53125 0.40625L5.84375 2.5ZM3.3125 2.5L5.09375 1.21875H2.90625L4.6875 2.5L4 0.40625L3.3125 2.5ZM0.78125 2.5L2.5625 1.21875H0.375L2.15625 2.5L1.46875 0.40625L0.78125 2.5Z" fill="#EEEEEE"/>
        </g>
      </g>
      <defs>
        <clipPath id="clip0_7132_166006">
          <rect width="16" height="16" fill="white"/>
        </clipPath>
      </defs>
    </svg>`;
  }

  getArabicFlag(): string {
    return `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <g clip-path="url(#clip0_7132_166007)">
        <mask id="mask0_7132_166007" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
          <path d="M8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16Z" fill="white"/>
        </mask>
        <g mask="url(#mask0_7132_166007)">
          <path d="M0 0H16V5.33333H0V0Z" fill="#006C35"/>
          <path d="M0 5.33333H16V10.6667H0V5.33333Z" fill="#FFFFFF"/>
          <path d="M0 10.6667H16V16H0V10.6667Z" fill="#000000"/>
        </g>
      </g>
      <defs>
        <clipPath id="clip0_7132_166007">
          <rect width="16" height="16" fill="white"/>
        </clipPath>
      </defs>
    </svg>`;
  }

  onSubmit(): void {
    debugger
    if (this.loginForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.loginForm.controls).forEach((key) => {
        const control = this.loginForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    const signInCommand = new SignInCommand({
      userName: this.loginForm.get('username')?.value,
      password: this.loginForm.get('password')?.value,
    });

    this.apiClient.signIn(signInCommand).subscribe({
      next: (response: IJwtAuthResponseBaseResponse) => {
        if (response.successed && response.data) {
          this.authService.login({
            accessToken: response.data.accessToken!,
            refreshToken: response.data.refreshToken!,
          });
         this.tokenService.setToken(response.data.accessToken!);


          if (response?.data?.userId != null) {
            localStorage.setItem('userId', response.data.userId.toString());
          }
          if (response.data?.isFirstLogin) {
            this.router.navigate(['/auth/change-password']);
          } else {
            this.errorModalService.showSuccess(
              this.translateService.instant('LOGIN_PAGE.LOGIN_SUCCESS')
            );
            this.router.navigate(['/admin/investment-funds']);
          }

        } else {
          this.errorModalService.showError(
            this.translateService.instant('LOGIN_PAGE.LOGIN_FAILED')
          );
        }
      },
      error: (error) => {
        console.error('Login error:', error);
        // Error handling is done by the error interceptor
      }
    });
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }
}
