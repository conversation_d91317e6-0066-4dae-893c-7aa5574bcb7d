<header class="container-fluid header-padding">
    <div class="d-flex justify-content-between align-items-center row ">
        <!-- Right side: Logo -->
        <div class="header-logo">
            <img src="assets/images/Logo.png" alt="Jadwa Investment" class="d-lg-none" />
        </div>
        <div class="col-md-4" >
            <h3 *ngIf="isDashboard()" class="header">{{'DASHBOARD.DASHBOARD'| translate }}</h3>
        </div>

        <!-- Center: Simple Search Bar -->
        <div class="search-section flex-grow-1 d-none d-lg-flex justify-content-center col-md-5">
            <div class="search-container">
                <svg xmlns="http://www.w3.org/2000/svg" width="19" height="18" viewBox="0 0 19 18" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8.79199 0.041687C4.30468 0.041687 0.666992 3.67937 0.666992 8.16669C0.666992 12.654 4.30468 16.2917 8.79199 16.2917C10.8107 16.2917 12.6574 15.5555 14.0784 14.337L17.5167 17.7753C17.7608 18.0194 18.1565 18.0194 18.4006 17.7753C18.6447 17.5312 18.6447 17.1355 18.4006 16.8914L14.9623 13.4531C16.1808 12.0321 16.917 10.1854 16.917 8.16669C16.917 3.67937 13.2793 0.041687 8.79199 0.041687ZM1.91699 8.16669C1.91699 4.36973 4.99504 1.29169 8.79199 1.29169C12.589 1.29169 15.667 4.36973 15.667 8.16669C15.667 11.9636 12.589 15.0417 8.79199 15.0417C4.99504 15.0417 1.91699 11.9636 1.91699 8.16669Z"
                        fill="#4F4F4F" />
                </svg>
                <input type="text" class="form-control search-input"
                    placeholder="{{ 'HEADER.SEARCH_PLACEHOLDER' | translate }}" [(ngModel)]="searchQuery"
                    (keyup.enter)="onSearch()" />

            </div>
        </div>

        <!-- Left side: Controls -->
        <div class="d-flex align-items-center gap-4 col-md-3 justify-content-between">


            <!-- Notification -->
            <div class="position-relative">
                <div class="card-header">

                    <div class="notification-container position-relative">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="26" viewBox="0 0 20 26" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M10.25 5.70831C7.00507 5.70831 4.32788 8.67163 4.15733 12.4522C4.15061 12.6012 4.14525 12.7591 4.13968 12.9232C4.11752 13.5765 4.09199 14.329 3.96332 15.0202C3.79263 15.9371 3.4282 16.8545 2.62213 17.5598C2.23049 17.9025 2 18.4403 2 19.0114C2 19.7289 2.47641 20.2916 3.05 20.2916H17.45C18.0236 20.2916 18.5 19.7289 18.5 19.0114C18.5 18.4403 18.2695 17.9025 17.8779 17.5598C17.0718 16.8545 16.7074 15.9371 16.5367 15.0202C16.408 14.329 16.3825 13.5765 16.3603 12.9232C16.3547 12.7591 16.3494 12.6012 16.3427 12.4522C16.1721 8.67163 13.4949 5.70831 10.25 5.70831ZM2.6594 12.3602C2.87188 7.65019 6.20728 3.95831 10.25 3.95831C14.2927 3.95831 17.6281 7.65019 17.8406 12.3602C17.8497 12.5615 17.8564 12.7542 17.8629 12.9399C17.8851 13.5717 17.9044 14.1217 18.0025 14.6488C18.1198 15.2786 18.3367 15.7738 18.7779 16.1598C19.5472 16.833 20 17.8895 20 19.0114C20 20.6229 18.9128 22.0416 17.45 22.0416H3.05C1.58719 22.0416 0.5 20.6229 0.5 19.0114C0.5 17.8895 0.952782 16.833 1.72213 16.1598C2.16329 15.7738 2.38025 15.2786 2.49749 14.6488C2.59561 14.1217 2.6149 13.5717 2.63705 12.9399C2.64356 12.7542 2.65032 12.5615 2.6594 12.3602Z"
                                fill="#00205A" />
                            <g opacity="0.4">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M10.25 0.458313C9.76698 0.458313 9.22367 0.555095 8.77856 0.89504C8.28476 1.27217 8 1.88261 8 2.64581C8 3.34134 8.23299 4.06769 8.58741 4.62398C8.93624 5.17151 9.50849 5.70831 10.25 5.70831C10.9915 5.70831 11.5638 5.17151 11.9126 4.62398C12.267 4.06769 12.5 3.34134 12.5 2.64581C12.5 1.88261 12.2152 1.27217 11.7214 0.89504C11.2763 0.555095 10.733 0.458313 10.25 0.458313ZM9.5 2.64581C9.5 2.52455 9.5204 2.46528 9.53165 2.44034C9.5423 2.41672 9.55979 2.39001 9.60012 2.35921C9.6979 2.28453 9.90459 2.20831 10.25 2.20831C10.5954 2.20831 10.8021 2.28453 10.8999 2.35921C10.9402 2.39001 10.9577 2.41672 10.9684 2.44034C10.9796 2.46528 11 2.52455 11 2.64581C11 2.91679 10.8972 3.28418 10.7087 3.58002C10.5147 3.88461 10.3369 3.95831 10.25 3.95831C10.1631 3.95831 9.98533 3.88461 9.79127 3.58002C9.60279 3.28418 9.5 2.91679 9.5 2.64581Z"
                                    fill="#00205A" />
                                <path
                                    d="M8 21.1666C8 20.6834 7.66421 20.2916 7.25 20.2916C6.83579 20.2916 6.5 20.6834 6.5 21.1666C6.5 23.5829 8.17893 25.5416 10.25 25.5416C12.3211 25.5416 14 23.5829 14 21.1666C14 20.6834 13.6642 20.2916 13.25 20.2916C12.8358 20.2916 12.5 20.6834 12.5 21.1666C12.5 22.6164 11.4926 23.7916 10.25 23.7916C9.00736 23.7916 8 22.6164 8 21.1666Z"
                                    fill="#00205A" />
                            </g>
                        </svg>
                        <span class="notification-badge">
                            {{notificationCount>99 ? '+99' : notificationCount}}
                        </span>
                    </div>
                </div>
            </div>

            <div class="d-flex align-items-center gap-2 mx-2 user-profile-section" (click)="navigateToProfile()">
                <img src="assets/images/5a88f6c30078d932a34b61c983a4185389144193.jpg"
                 class="rounded-circle" alt="User"
                    width="32" height="32" />
                <div class="d-flex align-items-center gap-1">
                    <span class="fw-medium text-primary fs-14 d-none d-lg-block">
                        {{fullName}}
                    </span>
                    <img src="assets/images/down.png" alt="Dropdown" />
                </div>
            </div>

            <!-- Language Dropdown -->
            <div class="dropdown d-none d-lg-block">
                <button class="btn lang-btn rounded-pill dropdown-toggle" type="button"
                        id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <div class="d-flex align-items-center gap-2">
                        <!-- Current Language Flag -->
                        <div class="language-flag" [innerHTML]="getCurrentLanguageFlag()"></div>
                        <span class="language-text">{{ getCurrentLanguageText() }}</span>
                    </div>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
                    <li>
                        <a class="dropdown-item d-flex align-items-center gap-2"
                           href="#" (click)="selectLanguage('en', $event)"
                           [class.active]="currentLang === 'en'">
                            <div class="language-flag" [innerHTML]="getEnglishFlag()"></div>
                            <span>English</span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center gap-2"
                           href="#" (click)="selectLanguage('ar', $event)"
                           [class.active]="currentLang === 'ar'">
                            <div class="language-flag" [innerHTML]="getArabicFlag()"></div>
                            <span>العربية</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Menu Toggle for Mobile -->
            <button class="btn d-lg-none" (click)="toggleSidenav()">
                <img src="assets/images/menu.png" alt="Menu" />
            </button>
        </div>
    </div>

    <!-- Dashboard Title -->

</header>
