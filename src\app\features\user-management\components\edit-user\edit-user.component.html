<!-- Main Content -->
<div class="edit-user-page">
  <!-- Breadcrumb -->
  <app-breadcrumb [breadcrumbs]="breadcrumbItems"></app-breadcrumb>

  <div class="mt-3">
    <!-- Page Header -->
    <app-page-header
      [title]="'USER_MANAGEMENT.EDIT.PAGE_TITLE' | translate">
    </app-page-header>
  </div>

  <!-- Form Container -->
  <div *ngIf="!isLoading || currentUserData" class="form-container mt-3">
      <!-- User Information Notice -->
      <!-- <div class="alert alert-info mb-3" role="alert">
        <i class="fas fa-info-circle me-2"></i>
        {{ 'USER_MANAGEMENT.EDIT.READONLY_FIELDS_NOTICE' | translate }}
      </div> -->

      <!-- Form Builder Component -->
      <app-form-builder
        [formGroup]="editUserForm"
        [formControls]="formControls"
        [isFormSubmitted]="isFormSubmitted"
        (valueChanged)="onValueChange($event.event, $event.control)"
        (keyPressed)="onKeyPressed($event.event, $event.control)"
               (removeSelection)="onRemoveSelectedItem($event)"
  (dropdownChanged)="onDropdownChange($event.event, $event.control)"
        (fileUploaded)="onFileUploaded($event)">
      </app-form-builder>

      <!-- Action Buttons -->
      <div class="actions justify-content-end d-flex mt-3 gap-3">
        <app-custom-button
          [btnName]="'COMMON.CANCEL' | translate"
          [buttonType]="ButtonTypeEnum.Secondary"
          (click)="onCancel()">
        </app-custom-button>

        <app-custom-button
          [btnName]="'USER_MANAGEMENT.EDIT.UPDATE' | translate"
          [buttonType]="ButtonTypeEnum.Primary"
          (click)="onSubmit()">
        </app-custom-button>
      </div>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && !currentUserData" class="error-container text-center mt-4">
    <div class="alert alert-danger" role="alert">
      <i class="fas fa-exclamation-triangle me-2"></i>
      {{ 'USER_MANAGEMENT.EDIT.USER_NOT_FOUND' | translate }}
    </div>
    <app-custom-button
      [btnName]="'COMMON.BACK_TO_LIST' | translate"
      [buttonType]="ButtonTypeEnum.Primary"
      (click)="onCancel()">
    </app-custom-button>
  </div>
</div>
