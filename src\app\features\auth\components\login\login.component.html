<div class="login">
  <div class="d-flex justify-content-between align-items-center margin-bottom-24 logo-container">
    <img [src]="currentLang =='en'? 'assets/images/logo-new-en.png' :'assets/icons/jadwa-logo.svg'" class="logo-img" alt="Jadwa logo" height="114" />
    <div class="lang-container">
      <button class="btn primary-btn lang-btn" (click)="changeLanguage()">
        <img src="assets/icons/icon-lang.png" class="mx-1" alt="language icon" />
        {{'LOGIN_PAGE.LANG' | translate}}
      </button>
    </div>
  </div>
  <div class="px-lg-3">
    <p style="line-height: normal;" class="font-size-xl navy-color bold-700 mt-3 mb-0 mb-lg-2">{{'LOGIN_PAGE.WELCOME_BACK' | translate}}
      <img class="d-lg-none d-md-inline" src="../../../../../assets/images/hand-wave.png" alt="hand wave" />
    </p>
    <p class="font-size-sm light-dark-color bold-400">{{'LOGIN_PAGE.APP_DESCRIPTION' | translate}}</p>
  </div>
  <div class="login-container" >
  <p class="font-size-m navy-color mb-0 bold-400">{{'LOGIN_PAGE.LOGIN' | translate}}</p>
  <img src="assets/icons/title-shape.png" class="" alt="icon"  />
  <p class="font-size-xxs light-dark-color-color bold-400 margin-bottom-24">{{'LOGIN_PAGE.ENTER_CREDENTIALS' | translate}}</p>


  <form class="row" [formGroup]="loginForm">
    <div class="col-md-12">
    <div class="margin-bottom-24">
      <label class="form-label font-size-xxs dark-color bold-700">{{'LOGIN_PAGE.USER_NAME' | translate}}</label>
      <input   class="form-control form-control-solid w-50"
      type="text" formControlName="username" />
      <div class="text-danger my-2" *ngIf="loginForm.get('username')?.touched && loginForm.get('username')?.invalid">
        {{'LOGIN_PAGE.USER_NAME_IS_REQUIRED' | translate}}
      </div>
    </div>

    <div class="mb-2 position-relative w-50 login-password">
      <label class="form-label font-size-xs dark bold-400">{{ 'LOGIN_PAGE.PASSWORD' | translate }} </label>
   <div>
    <div class="position-relative">
      <input
        [type]="showPassword ? 'text' : 'password'"
        class="form-control form-control-solid"
        formControlName="password"
      />
      <span
        class="eye-icon position-absolute"
        style="cursor: pointer"
        (click)="togglePasswordVisibility()"
      >
        <!-- <i [ngClass]="showPassword ? 'fa fa-eye-slash' : 'fa fa-eye'"></i> -->
        <img [src]="showPassword ? 'assets/images/eye-slash.png' :'assets/images/eye2.png'" alt="password" />
      </span>
    </div>
</div>


      <div class="text-danger my-2" *ngIf="loginForm.get('password')?.touched && loginForm.get('password')?.invalid">
        {{ 'LOGIN_PAGE.PASSWORD_IS_REQUIRED' | translate }}
      </div>
    </div>

        <div class="mt-4 w-50 d-flex flex-column justify-content-center align-items-center log-submit-btn">
          <app-custom-button
            type="submit"
            class="w-100 login-btn"
            [iconName]="iconName"
            [btnName]="'LOGIN_PAGE.LOGIN' | translate"
            (click)="onSubmit()"
          ></app-custom-button>
          <p class="bold-400 font-size-xxs dark-gray mt-5 d-none d-lg-block">
            <img src="assets/icons/info-login.png" class="" alt="info icon" />
            {{'LOGIN_PAGE.COPYRIGHT' | translate}}
          </p>
        </div>
      </div>
    </form>
  </div>
  <p class="bold-400 font-size-xxs grey d-lg-none d-flex flex-column align-items-center mt-3">
    <img src="assets/icons/login-icon-white.png" class="" alt="info icon" />
    {{'LOGIN_PAGE.COPYRIGHT' | translate}}
  </p>
</div>
